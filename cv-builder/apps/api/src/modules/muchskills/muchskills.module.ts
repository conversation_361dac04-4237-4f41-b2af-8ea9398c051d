import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { MuchskillsController } from './muchskills.controller';
import { MuchskillsService } from './muchskills.service';
import { Member, MemberSchema } from '../members/member.schema';
import { MembersModule } from '../members/members.module';
import { OrganizationModule } from '../organization/organization.module';
import {
  Organization,
  OrganizationSchema,
} from '../organization/organization.schema';

@Module({
  imports: [
    HttpModule,
    MembersModule,
    OrganizationModule,
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
    ]),
    MongooseModule.forFeature([{ name: Member.name, schema: MemberSchema }]),
  ],
  controllers: [MuchskillsController],
  providers: [MuchskillsService],
  exports: [MuchskillsService],
})
export class MuchskillsModule {}
