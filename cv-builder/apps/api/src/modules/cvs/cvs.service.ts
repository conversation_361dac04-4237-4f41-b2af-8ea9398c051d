import { ForbiddenError } from '@casl/ability';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import axios from 'axios';
import { escapeRegExp } from 'lodash';
import { Model, RootFilterQuery, Types } from 'mongoose';
import { CvStatus, Template, Message } from 'shared/types';
import { isMongoDuplicateKeyError } from 'src/utils';

import {
  Cv,
  CvDocument,
  CvSections,
  EducationDataItem,
  WorkHistoryDataItem,
} from './cv.schema';
import { CvPreferencesDto, UpdateCvDto, GetCvsDto } from './dto';
import { AiService } from '../ai/ai.service';
import { CaslAbilityFactory } from '../casl/casl-ability.factory';
import { Action } from '../casl/casl.types';
import { Customer, CustomerDocument } from '../customers/customer.schema';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { Member, MemberDocument } from '../members/member.schema';
import { UpdateCvSectionsDto } from './dto/update-cv-sections.dto';

@Injectable()
export class CvsService {
  constructor(
    @InjectModel(Cv.name) private cvModel: Model<CvDocument>,
    @InjectModel(Member.name) private memberModel: Model<MemberDocument>,
    @InjectModel(Customer.name) private customerModel: Model<CustomerDocument>,
    private caslAbilityFactory: CaslAbilityFactory,
    private aiService: AiService,
  ) {}

  async regenerateCv(
    cvId: string,
    query: string,
    authUser: AuthUserDto,
    messages?: Message[],
  ) {
    // First authorize access to the CV
    await this.findCvAndAuthorize(cvId, authUser, Action.Update);

    // Fetch CV with populated member and customer data
    const populatedCv = await this.cvModel
      .findById(cvId)
      .populate<{ member: MemberDocument }>('member')
      .populate<{
        'preferences.customer': CustomerDocument;
      }>('preferences.customer')
      .exec();

    if (!populatedCv) {
      throw new NotFoundException(`CV with ID "${cvId}" not found`);
    }

    const member = populatedCv.member;
    const customerName = (populatedCv.preferences.customer as CustomerDocument)
      ?.name;

    // Call AI service to regenerate CV sections
    const regeneratedSections = await this.aiService.regenerateCvSections(
      populatedCv,
      member,
      customerName,
      query,
      messages,
    );

    return regeneratedSections;
  }

  async getMemberCvs(memberId: string, authUser: AuthUserDto) {
    // First, verify that the member belongs to the same organization as the requesting user
    await this.findMemberAndAuthorize(memberId, authUser, Action.Read);

    // Then fetch CVs for the member, filtered by organization for extra security
    return await this.cvModel
      .find({
        member: memberId,
        organization: authUser.organization._id,
      })
      .populate<{ 'preferences.customer': CustomerDocument }>(
        'preferences.customer',
      );
  }

  async getCvById(cvId: string, authUser: AuthUserDto) {
    const cv = await this.cvModel.findById(cvId).populate<{
      'preferences.customer': CustomerDocument;
    }>('preferences.customer');

    if (!cv) {
      throw new NotFoundException(`CV with ID "${cvId}" not found`);
    }

    // First, verify that the member belongs to the same organization as the requesting user
    await this.findMemberAndAuthorize(
      cv.member as Types.ObjectId,
      authUser,
      Action.Read,
    );

    return cv;
  }

  async createCv(
    memberId: string,
    organizationId: Types.ObjectId,
    dto: CvPreferencesDto,
    authUser: AuthUserDto,
  ) {
    try {
      // First authorize access to the member
      const member = await this.findMemberAndAuthorize(
        memberId,
        authUser,
        Action.Read,
      );

      // Validate customer if provided
      if (dto.customer) {
        await this.validateCustomerBelongsToOrganization(
          dto.customer,
          organizationId,
        );
      }

      const sections: CvSections = {
        personalInfo: {
          order: 1,
          title: 'Personal Information',
          active: true,
          data: {
            firstName: { value: member.firstName || '', active: true },
            lastName: { value: member.lastName || '', active: true },
            jobTitle: { value: member.currentPosition || '', active: true },
            location: { value: member.location || '', active: true },
            nationality: { value: '', active: true },
            //Should we keep member email as '' in the member doc??
            email: { value: member.email || '', active: true },
            telephone: { value: member.telephone || '', active: true },
            socials: { inputs: member.socials, active: true },
          },
        },
        aboutMe: {
          order: 2,
          title: 'About Me',
          active: true,
          data: {
            description: '',
          },
        },
        workHistory: {
          order: 3,
          title: 'Work History',
          active: true,
          data: member.workExperience.map(
            (exp): WorkHistoryDataItem => ({
              active: true,
              companyName: exp.companyName,
              roleTitle: exp.roleTitle,
              description: exp.description,
              startDate: exp.startDate,
              endDate: exp.endDate,
              isCurrent: exp.isCurrent,
            }),
          ),
        },
        education: {
          order: 4,
          title: 'Education',
          active: true,
          data: member.education.map(
            (ed): EducationDataItem => ({
              active: true,
              schoolName: ed.schoolName,
              degree: ed.degree,
              description: ed.description,
              startDate: ed.startDate,
              endDate: ed.endDate,
            }),
          ),
        },
        certifications: {
          order: 5,
          title: 'Certifications',
          active: true,
          data: {
            description: member.certifications
              .map((cert) => ` - ${cert.name}`)
              .join(',\n'),
          },
        },
        skills: {
          order: 6,
          title: 'Skills',
          active: true,
          data: {
            description: member.skillsSummary,
          },
        },
        languages: {
          order: 7,
          title: 'Languages',
          active: true,
          data: {
            languages: member.languages,
          },
        },
      };

      const newCv = await this.cvModel.create({
        member: memberId,
        organization: organizationId,
        status: CvStatus.draft,
        template: Template.europass,
        preferences: dto,
        sections,
      });

      await this.memberModel.updateOne(
        { _id: memberId },
        {
          $push: { cvs: newCv.id },
        },
      );

      return newCv;
    } catch (error) {
      if (isMongoDuplicateKeyError(error)) {
        throw new BadRequestException('Member already has CV with this alias.');
      }

      if (error instanceof Error) {
        throw new Error(error.message);
      } else {
        throw new Error(String(error));
      }
    }
  }

  async updateCv(
    cvId: string | Types.ObjectId,
    dto: UpdateCvDto,
    authUser: AuthUserDto,
  ) {
    try {
      // First authorize access to the CV
      await this.findCvAndAuthorize(cvId, authUser, Action.Update);

      // Validate customer if provided
      if (dto.preferences?.customer) {
        const cv = await this.cvModel.findById(cvId);
        if (!cv) {
          throw new BadRequestException('CV not found');
        }
        await this.validateCustomerBelongsToOrganization(
          dto.preferences.customer,
          cv.organization as Types.ObjectId,
        );
      }

      return await this.cvModel.findByIdAndUpdate(
        cvId,
        { $set: { ...dto } },
        { new: true },
      );
    } catch (error) {
      if (isMongoDuplicateKeyError(error)) {
        throw new BadRequestException('Member already has CV with this alias.');
      }

      if (error instanceof Error) {
        throw new Error(error.message);
      } else {
        throw new Error(String(error));
      }
    }
  }

  async updateCvSections(
    cvId: string | Types.ObjectId,
    dto: UpdateCvSectionsDto,
    authUser: AuthUserDto,
  ) {
    // First authorize access to the CV
    await this.findCvAndAuthorize(cvId, authUser, Action.Update);

    return await this.cvModel.findByIdAndUpdate(cvId, {
      $set: { sections: dto },
    });
  }

  async duplicateCv(cvId: string | Types.ObjectId, authUser: AuthUserDto) {
    // First authorize access to the original CV
    const originalCv = await this.findCvAndAuthorize(
      cvId,
      authUser,
      Action.Read,
    );

    let counter = 1;
    while (
      await this.cvModel.exists({
        member: originalCv.member,
        'preferences.title': `${originalCv.preferences.title} duplicate #${counter}`,
      })
    ) {
      counter++;
    }

    const duplicatedData = originalCv.toObject();
    delete duplicatedData._id;

    const newCv = await this.cvModel.create({
      ...duplicatedData,
      'preferences.title': `${originalCv.preferences.title} duplicate #${counter}`,
    });

    // Add the new CV to the member's CVs array
    await this.memberModel.updateOne(
      { _id: originalCv.member },
      {
        $push: { cvs: newCv.id },
      },
    );

    return newCv;
  }

  async deleteCv(cvId: string | Types.ObjectId, authUser: AuthUserDto) {
    // First authorize access to the CV
    const cv = await this.findCvAndAuthorize(cvId, authUser, Action.Delete);

    // Remove the CV from the member's CVs array
    await this.memberModel.updateOne(
      { _id: cv.member },
      {
        $pull: { cvs: cvId },
      },
    );

    return await this.cvModel.findByIdAndDelete(cvId);
  }

  async deleteOrganizationCvs(organizationId: string | Types.ObjectId) {
    await this.cvModel.deleteMany({ organization: organizationId });
  }

  async getMemberBase64Avatar(
    memberId: string | Types.ObjectId,
    authUser: AuthUserDto,
  ) {
    try {
      // First authorize access to the member
      const member = await this.findMemberAndAuthorize(
        memberId,
        authUser,
        Action.Read,
      );

      const avatarUrl = member.avatar;

      if (!avatarUrl) {
        return null;
      }

      const response = await axios.get(avatarUrl, {
        responseType: 'arraybuffer',
      });
      const contentType = response.headers['content-type'] || 'image/png';
      const base64 = Buffer.from(response.data, 'binary').toString('base64');
      return `data:${contentType};base64,${base64}`;
    } catch (err) {
      console.error(err);
      throw new Error('Something went wrong!');
    }
  }

  private async findCvAndAuthorize(
    cvId: string | Types.ObjectId,
    authUser: AuthUserDto,
    action: Action,
  ): Promise<CvDocument> {
    const cv = await this.cvModel.findById(cvId);
    if (!cv) {
      throw new NotFoundException(`CV with ID "${cvId}" not found`);
    }

    const ability = this.caslAbilityFactory.createForUser(authUser);

    const cvForCheck = {
      ...cv.toObject(),
      constructor: Cv,
      organization: cv.organization.toString(),
    } as unknown as Cv & { organization: string };

    try {
      ForbiddenError.from(ability).throwUnlessCan(action, cvForCheck);
    } catch (error) {
      if (error instanceof ForbiddenError) {
        throw new ForbiddenException(error.message);
      }
      throw error;
    }

    return cv;
  }

  private async findMemberAndAuthorize(
    memberId: string | Types.ObjectId,
    authUser: AuthUserDto,
    action: Action,
  ): Promise<MemberDocument> {
    const member = await this.memberModel.findById(memberId);
    if (!member) {
      throw new NotFoundException(`Member with ID "${memberId}" not found`);
    }

    const ability = this.caslAbilityFactory.createForUser(authUser);

    const memberForCheck = {
      ...member.toObject(),
      constructor: Member,
      organization: member.organization.toString(),
    } as unknown as Member & { organization: string };

    try {
      ForbiddenError.from(ability).throwUnlessCan(action, memberForCheck);
    } catch (error) {
      if (error instanceof ForbiddenError) {
        throw new ForbiddenException(error.message);
      }
      throw error;
    }

    return member;
  }

  async getCvs(
    query: GetCvsDto,
    user: AuthUserDto,
  ): Promise<{ cvs: CvDocument[]; total: number }> {
    const {
      page = 1,
      itemsPerPage = 12,
      search,
      sortBy = 'title',
      sortOrder = 'asc',
      status = CvStatus.draft,
    } = query;

    const filter: RootFilterQuery<CvDocument> = {
      organization: user.organization._id.toString(),
      status,
    };

    if (search?.trim()) {
      const escapedSearchTerm = escapeRegExp(search.trim());
      filter['preferences.title'] = new RegExp(escapedSearchTerm, 'i');
    }

    const sortField = sortBy === 'title' ? 'preferences.title' : 'updatedAt';
    const sortDirection = sortOrder === 'asc' ? 1 : -1;
    const skip = (page - 1) * itemsPerPage;

    const [cvs, total] = await Promise.all([
      this.cvModel
        .find(filter)
        .populate<{ member: MemberDocument }>(
          'member',
          'firstName lastName avatar',
        )
        .populate<{ 'preferences.customer': CustomerDocument }>(
          'preferences.customer',
          'name',
        )
        .sort({ [sortField]: sortDirection })
        .skip(skip)
        .limit(itemsPerPage)
        .exec(),
      this.cvModel.countDocuments(filter),
    ]);

    return { cvs, total };
  }

  private async validateCustomerBelongsToOrganization(
    customerId: string,
    organizationId: Types.ObjectId,
  ) {
    const customer = await this.customerModel.findById(customerId);
    if (!customer) {
      throw new BadRequestException('Customer not found');
    }
    if (customer.organization.toString() !== organizationId.toString()) {
      throw new BadRequestException(
        'Customer does not belong to the same organization',
      );
    }
  }
}
