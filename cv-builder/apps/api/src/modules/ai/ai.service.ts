import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { AxiosError } from 'axios';
import { firstValueFrom } from 'rxjs';
import { Message } from 'shared/types';

import { CvDocument, CvSections } from '../cvs/cv.schema';
import { MemberDocument } from '../members/member.schema';

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);

  constructor(private readonly httpService: HttpService) {}

  async regenerateCvSections(
    cv: CvDocument,
    member: MemberDocument,
    customerName: string | null,
    query: string,
    messages?: Message[],
  ): Promise<Partial<CvSections>> {
    try {
      const result = await firstValueFrom(
        this.httpService.post(process.env.AI_API_URL + '/ai/regenerate-cv', {
          cv: cv.toObject(),
          member: member.toObject(),
          customerName: customerName,
          query,
          messages: messages || [],
          token: process.env.AI_API_TOKEN,
        }),
      );

      const response = result?.data;

      // Check if there was an error from the AI service
      if (response?.error) {
        throw new BadRequestException(
          response.changesSummary || 'No changesSummary returned from AI',
        );
      }

      return response;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      if (error instanceof AxiosError && error.response?.status === 400) {
        this.logger.error(
          'AI service returned a bad request.',
          error.stack,
          error.message,
        );
        throw new BadRequestException('AI Service is not available');
      }

      if (error instanceof Error) {
        this.logger.error(
          'An error occurred in regenerateCvSections',
          error.message,
          error.stack,
        );
        throw new BadRequestException(error.message);
      }

      this.logger.error(
        'An unknown error occurred in regenerateCvSections',
        error,
      );
      throw new BadRequestException('An unknown error occurred');
    }
  }
}
