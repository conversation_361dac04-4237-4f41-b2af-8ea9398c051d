import { useMutation, useQueryClient } from '@tanstack/react-query';
import { isAxiosError } from 'axios';
import {
  createContext,
  useContext,
  useEffect,
  useState,
  useMemo,
  ReactNode,
} from 'react';
import { useNavigate } from 'react-router-dom';
import { SignInDto } from 'shared/dto/auth/sign-in.dto';
import { User, UserRole } from 'shared/types';

import api from '@/api';
import { NAVIGATE_PATH } from '@/helpers/constants';
import {
  getCurrentUserRequest,
  setActiveOrganizationRequest,
} from '@/helpers/requests';

export interface OrganizationData {
  _id: string;
  name: string;
  photo?: string | null;
  planTier?: string;
  planStatus?: string;
  deletionScheduledDate?: Date;
  aiContext?: string;
  muchskillsIntegration?: {
    token: string;
    connected: boolean;
    lastSync?: Date;
  };
}

interface UserResponse {
  data: {
    user: User;
    organization: OrganizationData;
    needsOrganizationCreation: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  organization: OrganizationData | null;
  loading: boolean;
  needsOrganizationCreation: boolean;
  login: (credentials: SignInDto) => Promise<{ message: string }>;
  logout: () => Promise<void>;
  switchOrganization: (orgId: string) => Promise<void>;
  refreshUserData: () => Promise<void>;

  // Simple role-based helpers
  isOwner: boolean;
  isAdmin: boolean;
  isMember: boolean;
  canDeleteOrganization: boolean;
  canManageUsers: boolean;
  canUpdateOrganization: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [organization, setOrganization] = useState<OrganizationData | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [needsOrganizationCreation, setNeedsOrganizationCreation] =
    useState(false);
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const switchOrganizationMutation = useMutation({
    mutationFn: setActiveOrganizationRequest,
    onSuccess: async () => {
      navigate(NAVIGATE_PATH.home);
      await queryClient.invalidateQueries();
      const userData = await getCurrentUserRequest();
      _updateUserAndOrg({ data: userData });
    },
  });

  const _updateUserAndOrg = (userResponse: UserResponse) => {
    setUser(userResponse.data.user);
    if (userResponse.data.needsOrganizationCreation) {
      setNeedsOrganizationCreation(true);
      setOrganization(null);
    } else {
      setOrganization(userResponse.data.organization);
      setNeedsOrganizationCreation(false);
    }
  };

  // Check user status on app initialization
  useEffect(() => {
    const checkUserStatus = async () => {
      try {
        const userData = await getCurrentUserRequest();
        _updateUserAndOrg({ data: userData });
      } catch (error) {
        if (isAxiosError(error)) {
          const message = error.response?.data?.message;
          console.error(
            'Error checking user status:',
            message || error.message,
          );
        } else {
          console.error('Unexpected error:', error);
        }
        // If 401, user is not authenticated
        setUser(null);
        setOrganization(null);
        setNeedsOrganizationCreation(false);
      } finally {
        setLoading(false);
      }
    };

    checkUserStatus();
  }, []);

  const login = async (credentials: SignInDto & { token?: string }) => {
    const { token, ...loginData } = credentials;
    const response = await api.post('/login', loginData, {
      params: token ? { token } : undefined,
    });

    // After successful login, fetch user data
    const userResponse = await api.get('/me');
    _updateUserAndOrg(userResponse);

    return response.data;
  };

  const logout = async () => {
    await api.post('/sign-out');
    setUser(null);
    setOrganization(null);
    setNeedsOrganizationCreation(false);
    navigate(NAVIGATE_PATH.login);
  };

  const switchOrganization = async (orgId: string) => {
    await switchOrganizationMutation.mutateAsync(orgId);
  };

  const refreshUserData = async () => {
    try {
      const userData = await getCurrentUserRequest();
      _updateUserAndOrg({ data: userData });
    } catch (error) {
      console.error('Error refreshing user data:', error);
      throw error;
    }
  };

  // Computed role-based helpers
  const isOwner = user?.role === UserRole.OWNER;
  const isAdmin = user?.role === UserRole.ADMIN;
  const isMember = user?.role === UserRole.MEMBER;
  const canDeleteOrganization = isOwner;
  const canManageUsers = isOwner || isAdmin;
  const canUpdateOrganization = isOwner || isAdmin;

  const value = useMemo(
    () => ({
      user,
      organization,
      loading,
      needsOrganizationCreation,
      login,
      logout,
      switchOrganization,
      refreshUserData,
      isOwner,
      isAdmin,
      isMember,
      canDeleteOrganization,
      canUpdateOrganization,
      canManageUsers,
    }),
    [
      user,
      organization,
      loading,
      needsOrganizationCreation,
      isOwner,
      isAdmin,
      isMember,
      canDeleteOrganization,
      canUpdateOrganization,
      canManageUsers,
    ],
  );

  // Show loading spinner while checking authentication status
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
