import { DndContext, closestCenter, DragEndEvent } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Dispatch } from 'react';
import {
  AboutMeData,
  CertificationsData,
  CustomSectionData,
  EducationDataItem,
  LanguagesData,
  PersonalInfoData,
  Section,
  SkillsData,
  WorkHistoryDataItem,
} from 'shared/types';

import {
  AboutMeForm,
  Accordion,
  CertificationsForm,
  CustomSection,
  EducationForm,
  LanguagesForm,
  PersonalInfoForm,
  SkillsForm,
  WorkHistoryForm,
} from './components';

import { FormReducerAction, ReducerActionType } from '@/pages';

interface CvFormProps {
  sortedSections: Section[];
  onSectionUpdate: Dispatch<FormReducerAction>;
  addNewSection: () => void;
}

export function CvForm({
  sortedSections,
  onSectionUpdate,
  addNewSection,
}: CvFormProps) {
  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const activeSectionIndex = sortedSections.findIndex(
        (section) => section.id === active.id,
      );
      const overSectionIndex = sortedSections.findIndex(
        (section) => section.id === over?.id,
      );

      arrayMove(sortedSections, activeSectionIndex, overSectionIndex).forEach(
        (section, index) =>
          onSectionUpdate({
            type: ReducerActionType.updateOrder,
            sectionId: section.id,
            order: index + 1,
          }),
      );
    }
  }

  const renderFormSection = (section: Section) => {
    switch (section.id) {
      case 'personalInfo':
        return (
          <PersonalInfoForm
            sectionId={section.id}
            data={section.data as PersonalInfoData}
            onDataChange={onSectionUpdate}
          />
        );
      case 'aboutMe':
        return (
          <AboutMeForm
            sectionId={section.id}
            data={section.data as AboutMeData}
            onDataChange={onSectionUpdate}
          />
        );
      case 'workHistory':
        return (
          <WorkHistoryForm
            sectionId={section.id}
            data={section.data as WorkHistoryDataItem[]}
            onDataChange={onSectionUpdate}
          />
        );
      case 'education':
        return (
          <EducationForm
            sectionId={section.id}
            data={section.data as EducationDataItem[]}
            onDataChange={onSectionUpdate}
          />
        );
      case 'certifications':
        return (
          <CertificationsForm
            sectionId={section.id}
            data={section.data as CertificationsData}
            onDataChange={onSectionUpdate}
          />
        );
      case 'skills':
        return (
          <SkillsForm
            sectionId={section.id}
            data={section.data as SkillsData}
            onDataChange={onSectionUpdate}
          />
        );
      case 'languages':
        return (
          <LanguagesForm
            sectionId={section.id}
            data={section.data as LanguagesData}
            onDataChange={onSectionUpdate}
          />
        );
      default:
        return (
          <CustomSection
            sectionId={section.id}
            data={section.data as CustomSectionData}
            onDataChange={onSectionUpdate}
          />
        );
    }
  };

  return (
    <div className="flex flex-col pb-20 w-full h-full overflow-auto">
      <DndContext
        modifiers={[restrictToVerticalAxis]}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={sortedSections}
          strategy={verticalListSortingStrategy}
        >
          {sortedSections.map((section) => (
            <Accordion
              key={section.id}
              section={section}
              hideCheckbox={section.id === 'personalInfo'}
              onUpdate={(sectionData) => {
                onSectionUpdate({
                  type: ReducerActionType.updateSection,
                  sectionId: section.id,
                  title: sectionData.title,
                  active: sectionData.active,
                });
              }}
              onRemove={() =>
                onSectionUpdate({
                  type: ReducerActionType.removeSection,
                  sectionId: section.id,
                })
              }
            >
              {renderFormSection(section)}
            </Accordion>
          ))}
        </SortableContext>
      </DndContext>
      <div
        className="m-4 p-4 flex flex-col space-y-2 items-center justify-center border border-msGray-5 rounded-[8px] text-msGray-4 hover:border-msBlue-2 hover:text-msBlue-2 hover:bg-msBlue-4 transition-all cursor-pointer"
        onClick={addNewSection}
      >
        <span className="text-smalldoge-3">+ Add custom section</span>
      </div>
    </div>
  );
}
