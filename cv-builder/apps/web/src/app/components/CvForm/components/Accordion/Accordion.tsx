import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import classNames from 'classnames';
import { ChevronRight, Trash2, Sparkles } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { SectionBase } from 'shared/types';

import { ButtonSecondary, Checkbox, Icon, Input } from '../../../../components';

import { cn } from '@/lib/utils';

interface AccordionProps {
  section: SectionBase;
  children: React.ReactNode;
  onUpdate: (val: SectionBase) => void;
  onRemove: () => void;
  hideCheckbox?: boolean;
}

export const Accordion = React.memo(
  ({ section, children, onUpdate, onRemove, hideCheckbox }: AccordionProps) => {
    const [open, setOpen] = useState<boolean>(false);
    const [titleError, setTitleError] = useState<string>();
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({ id: section.id });

    const style = {
      zIndex: isDragging ? 1 : 'auto',
      transform: CSS.Translate.toString(transform),
      transition,
    };

    useEffect(() => {
      setTitleError(undefined);

      if (!section.title.length) setTitleError('Title is required');
    }, [section.title]);

    return (
      <div
        ref={setNodeRef}
        style={style}
        className="relative w-full p-4 border-b border-msGray-5 pl-7 h-fit bg-msWhite group"
      >
        {/* Drag handle */}
        <div
          className="absolute top-[calc(50%-12px)] left-0.5 rounded-md transition-all duration-300 hover:bg-msGray-6 cursor-grab opacity-0 group-hover:opacity-100"
          {...attributes}
          {...listeners}
        >
          <Icon source="move" size={24} fillColor="msGray-3" />
        </div>
        {/* Tab content */}
        <div className="flex items-center justify-between space-x-2">
          <Input
            className="w-full px-1 font-black border-0 shadow-none outline-none text-bigdoge-7 hover:bg-msGray-6"
            errorClassName="px-1"
            wrapperClassName="w-full"
            value={section.title}
            placeholder="Section title"
            error={titleError}
            onChange={(e) => onUpdate({ ...section, title: e.target.value })}
          />
          <div className="flex items-center space-x-1">
            {section.id.includes('skills') && (
              <button
                className="transition-all duration-300 text-msMagicAi-1 hover:text-msMagicAi-2"
                onClick={() => setOpen((val) => !val)}
              >
                <Sparkles size={16} />
              </button>
            )}
            {section.id.includes('customSection') && (
              <button
                className="transition-all duration-300 opacity-0 group-hover:opacity-100"
                onClick={onRemove}
              >
                <Trash2 size={16} />
              </button>
            )}
            {!hideCheckbox && (
              <Checkbox
                checked={section.active}
                onCheckedChange={(val) =>
                  onUpdate({
                    ...section,
                    active: !!val,
                  })
                }
              />
            )}
            <ButtonSecondary
              variant={'icon'}
              onClick={() => setOpen((val) => !val)}
            >
              <ChevronRight
                className={cn(
                  'transition-all duration-300',
                  open && 'transform rotate-90',
                )}
                size={14}
              />
            </ButtonSecondary>
          </div>
        </div>
        {/* Collapsable content */}
        <div
          className={classNames(
            'grid transition-grid-rows duration-300 ease-in-out',
            open ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]',
          )}
        >
          <div className="overflow-hidden">{children}</div>
        </div>
      </div>
    );
  },
);
