import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { uniqueId } from 'lodash';
import { ChevronLeft } from 'lucide-react';
import {
  useCallback,
  useContext,
  useEffect,
  useReducer,
  useState,
} from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  AboutMeData,
  CertificationsData,
  CustomSection,
  EducationDataItem,
  LanguagesData,
  PersonalInfoData,
  Section,
  SectionData,
  SkillsData,
  WorkHistoryDataItem,
  CvStatus,
} from 'shared/types';
import { toast } from 'sonner';
import { useDebounce } from 'use-debounce';

import { createTruncatedDisplayName } from '../helpers/nameUtils';
import {
  getCvById,
  getMemberByIdRequest,
  updateCvSectionsRequest,
} from '../helpers/requests';

import {
  ButtonSecondary,
  Drawer,
  CvForm,
  CvSettingsForm,
  CvPreview,
  MemberBreadcrumb,
} from '@/components';
import { LayoutContext } from '@/components/Layout/Layout';
import { NAVIGATE_PATH } from '@/helpers/constants';
import { updateDraftCvTimestamp } from '@/hooks/useCvMutations';
import { useResponsive } from '@/hooks/useResponsive';

export enum ReducerActionType {
  setInitialSections = 'setInitialSections',
  updateSection = 'updateSection',
  updateField = 'updateField',
  updateOrder = 'updateOrder',
  addSection = 'addSection',
  removeSection = 'removeSection',
  updateAiSections = 'updateAiSections',
  revertToSnapshot = 'revertToSnapshot',
}

export type FormReducerAction = {
  type: ReducerActionType;
  sectionId?: string;
  data?: SectionData;
  initialSections?: Section[];
  title?: string;
  active?: boolean;
  order?: number;
  aiSections?: any;
  customSections?: CustomSection[];
  updatedSections?: {
    // TODO(vladyslav): use Section type
    sections?: any;
    customSections?: CustomSection[];
  };
  snapshot?: Section[];
};

function sectionsReducer(sections: Section[], action: FormReducerAction) {
  switch (action.type) {
    case ReducerActionType.setInitialSections:
      return action.initialSections as Section[];

    case ReducerActionType.updateField:
      return sections.map((section) => {
        if (section.id === action.sectionId) {
          return { ...section, data: action.data } as Section;
        }
        return section;
      });

    case ReducerActionType.updateSection:
      return sections.map((section) => {
        if (section.id === action.sectionId) {
          return {
            ...section,
            title: action.title,
            active: action.active,
          } as Section;
        }
        return section;
      });

    case ReducerActionType.updateOrder:
      return sections.map((section) => {
        if (section.id === action.sectionId && section.order !== action.order) {
          return { ...section, order: action.order } as Section;
        }
        return section;
      });

    case ReducerActionType.addSection:
      return sections.concat({
        id: 'customSection' + uniqueId(),
        order: sections.length + 1,
        title: 'Custom section',
        active: true,
        data: {
          description: '',
        },
      });

    case ReducerActionType.removeSection:
      return sections.filter((s) => s.id !== action.sectionId);

    case ReducerActionType.updateAiSections: {
      // Handle new updatedSections structure
      if (action.updatedSections) {
        // Update existing sections
        const updatedSections = sections.map((section) => {
          const aiSection = action.updatedSections?.sections?.[section.id];
          if (aiSection) {
            return {
              ...section,
              data: aiSection.data,
            };
          }
          return section;
        });

        // Add or update custom sections if provided
        if (
          action.updatedSections.customSections &&
          action.updatedSections.customSections.length > 0
        ) {
          // Remove existing custom sections and add new ones
          const nonCustomSections = updatedSections.filter(
            (s) => !s.id.includes('customSection'),
          );

          // Transform AI custom sections to match the expected format
          const transformedCustomSections =
            action.updatedSections.customSections.map((customSection) => ({
              ...customSection,
              id: customSection.id.includes('customSection')
                ? customSection.id
                : 'customSection' + customSection.id,
            }));

          return [...nonCustomSections, ...transformedCustomSections];
        }

        return updatedSections;
      }

      // Fallback to old structure for backward compatibility
      const updatedSections = sections.map((section) => {
        const aiSection = action.aiSections?.[section.id];
        if (aiSection) {
          return {
            ...section,
            data: aiSection.data,
          };
        }
        return section;
      });

      if (action.customSections && action.customSections.length > 0) {
        const nonCustomSections = updatedSections.filter(
          (s) => !s.id.includes('customSection'),
        );

        const transformedCustomSections = action.customSections.map(
          (customSection) => ({
            ...customSection,
            id: customSection.id.includes('customSection')
              ? customSection.id
              : 'customSection' + customSection.id,
          }),
        );

        return [...nonCustomSections, ...transformedCustomSections];
      }

      return updatedSections;
    }

    case ReducerActionType.revertToSnapshot:
      return action.snapshot ? [...action.snapshot] : sections;

    default:
      return sections;
  }
}

export function CVEditPage() {
  const navigate = useNavigate();
  const { setHeaderCallback } = useContext(LayoutContext);
  const { memberId, cvId } = useParams();
  const { isSmallScreen } = useResponsive();
  const queryClient = useQueryClient();

  const [previewActive, setPreviewActive] = useState<boolean>(false);
  const [settingsActive, setSettingsActive] = useState<boolean>(false);

  const [sections, dispatchAction] = useReducer(sectionsReducer, []);
  const [debouncedSections] = useDebounce(sections, 600);
  const [preAiSnapshot, setPreAiSnapshot] = useState<Section[] | null>(null);
  const [hasAiChanges, setHasAiChanges] = useState<boolean>(false);

  const { data: member } = useQuery({
    enabled: !!memberId,
    queryKey: ['member', { memberId }],
    queryFn: () => getMemberByIdRequest(memberId as string),
  });

  const { data: cv } = useQuery({
    enabled: !!cvId,
    queryKey: ['cv', { cvId }],
    queryFn: () => getCvById(cvId as string),
  });

  useEffect(() => {
    if (!cv) return;

    const defaultSections = Object.entries(cv.sections)
      .filter(([key, value]) => key !== '_id' && key !== 'customSections')
      .map(([key, value]) => ({
        id: key,
        ...value,
      }));

    const customSections = cv.sections.customSections.map((sec) => ({
      id: 'customSection' + uniqueId(),
      ...sec,
    }));

    dispatchAction({
      type: ReducerActionType.setInitialSections,
      initialSections: [...defaultSections, ...customSections],
    });
  }, [cv]);

  const { mutate: updateCvSections } = useMutation({
    mutationFn: () =>
      updateCvSectionsRequest(cvId as string, {
        personalInfo: {
          ...(sections.find((s) => s.id === 'personalInfo') as Section),
          data: (sections.find((s) => s.id === 'personalInfo') as Section)
            .data as PersonalInfoData,
        },
        aboutMe: {
          ...(sections.find((s) => s.id === 'aboutMe') as Section),
          data: (sections.find((s) => s.id === 'aboutMe') as Section)
            .data as AboutMeData,
        },
        workHistory: {
          ...(sections.find((s) => s.id === 'workHistory') as Section),
          data: (sections.find((s) => s.id === 'workHistory') as Section)
            .data as WorkHistoryDataItem[],
        },
        education: {
          ...(sections.find((s) => s.id === 'education') as Section),
          data: (sections.find((s) => s.id === 'education') as Section)
            .data as EducationDataItem[],
        },
        certifications: {
          ...(sections.find((s) => s.id === 'certifications') as Section),
          data: (sections.find((s) => s.id === 'certifications') as Section)
            .data as CertificationsData,
        },
        skills: {
          ...(sections.find((s) => s.id === 'skills') as Section),
          data: (sections.find((s) => s.id === 'skills') as Section)
            .data as SkillsData,
        },
        languages: {
          ...(sections.find((s) => s.id === 'languages') as Section),
          data: (sections.find((s) => s.id === 'languages') as Section)
            .data as LanguagesData,
        },
        customSections: sections.filter((s) =>
          s.id.includes('customSection'),
        ) as CustomSection[],
      }),
    onSuccess: () => {
      toast.success('CV updated');
      updateDraftCvTimestamp(
        queryClient,
        cvId as string,
        cv?.status as CvStatus,
      );
    },
    onError: (e) => {
      console.log(e);
      toast.error('Please fix invalid data before submitting.');
    },
  });

  useEffect(() => {
    if (cv) {
      setHeaderCallback(cv.preferences.title);
    }

    return () => setHeaderCallback('');
  }, [setHeaderCallback, cv]);

  const handleCreateSnapshot = useCallback(() => {
    if (!preAiSnapshot) {
      setPreAiSnapshot([...sections]);
    }
  }, [sections, preAiSnapshot]);

  const handleRevertToSnapshot = useCallback(() => {
    if (preAiSnapshot) {
      dispatchAction({
        type: ReducerActionType.revertToSnapshot,
        snapshot: preAiSnapshot,
      });
      setHasAiChanges(false);
      // Trigger save after revert
      updateCvSections();
    }
  }, [preAiSnapshot, updateCvSections]);

  const cvSettings = () => {
    if (!member) return;

    return (
      <CvSettingsForm
        memberId={member._id}
        cv={cv}
        onCanceled={() => setSettingsActive(false)}
        onSubmitted={() => setSettingsActive(false)}
      />
    );
  };

  const cvPreview = () => {
    if (!cv || !member) return;

    return (
      <CvPreview
        cv={cv}
        member={member}
        sections={debouncedSections}
        editPage={true}
        hasAiChanges={hasAiChanges}
        onSettingsEdit={() => setSettingsActive(true)}
        onAiSectionsUpdate={(response) => {
          // Only update sections if they exist (handle bad prompt case)
          if (response.updatedSections) {
            dispatchAction({
              type: ReducerActionType.updateAiSections,
              updatedSections: response.updatedSections,
            });
            setHasAiChanges(true);
          }
        }}
        onCreateSnapshot={handleCreateSnapshot}
        onRevertToSnapshot={handleRevertToSnapshot}
      />
    );
  };

  if (!member || !cv) return null;

  return (
    <div className="flex h-full">
      <div className="flex flex-col w-full">
        <div className="flex h-10 px-4 py-2 border-b border-msGray-5">
          <ButtonSecondary
            padding="iconLeft"
            onClick={() => navigate(`${NAVIGATE_PATH.cvList}/${member._id}`)}
          >
            <span className="flex items-center">
              <ChevronLeft size={16} />
              <span>Back to List</span>
            </span>
          </ButtonSecondary>
          <div className="flex ml-auto space-x-2">
            {isSmallScreen && (
              <ButtonSecondary onClick={() => setPreviewActive(true)}>
                Preview
              </ButtonSecondary>
            )}
            {cv && (
              <ButtonSecondary onClick={() => updateCvSections()}>
                Save Changes
              </ButtonSecondary>
            )}
          </div>
        </div>
        <div className="px-4 py-2 border-b border-msGray-5">
          <MemberBreadcrumb
            memberName={createTruncatedDisplayName(
              member.firstName,
              member.lastName,
              20,
            )}
            isSmallScreen={isSmallScreen}
            mode="edit"
            cvName={cv.preferences.title}
            onMemberClick={() => {
              navigate(`${NAVIGATE_PATH.cvList}/${member._id}`);
            }}
          />
        </div>
        <CvForm
          sortedSections={sections.sort((a, b) => a.order - b.order)}
          onSectionUpdate={dispatchAction}
          addNewSection={() =>
            dispatchAction({ type: ReducerActionType.addSection })
          }
        />
      </div>

      {isSmallScreen ? (
        <>
          <Drawer
            active={previewActive}
            onClose={() => setPreviewActive(false)}
          >
            {cvPreview()}
          </Drawer>
          <Drawer
            active={settingsActive}
            onClose={() => setSettingsActive(false)}
          >
            <div className="overflow-auto">{cvSettings()}</div>
          </Drawer>
        </>
      ) : (
        <div className="border-l border-msGray-5 w-[640px] flex-shrink-0">
          {settingsActive ? (
            <div className="p-5">{cvSettings()}</div>
          ) : (
            cvPreview()
          )}
        </div>
      )}
    </div>
  );
}
