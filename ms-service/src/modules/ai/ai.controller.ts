import { Body, Controller, Post, UnauthorizedException } from '@nestjs/common';

import { AiService } from './ai.service';

@Controller()
export class AiController {
  constructor(private readonly aiService: AiService) {}

  // new
  @Post('ai/get-analysis-data')
  async getAnalysisData(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.query) {
      throw new Error('no query param');
    }

    try {
      const data = await this.aiService.getAnalysisData({
        query: body.query,
      });

      if (!data) {
        return { error: true, data: null };
      }

      return { data, error: false };
    } catch (error) {
      console.log('getAnalysisData error: ', error);
      return { data: null, error: true };
    }
  }

  @Post('ai/embeddings')
  async generateEmbeddings(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.text) {
      throw new Error('no text param');
    }

    try {
      const embeddings = await this.aiService.generateEmbeddings(
        body.text,
        3,
        500,
        body.dimensions,
      );
      if (!embeddings) {
        return { error: true };
      }
      return { embeddings, error: false };
    } catch (error) {
      console.log('generateEmbeddings error: ', error);
      return { embeddings: null, error: true };
    }
  }

  @Post('ai/get-skills-for-analysis')
  async getSkillsForAnalysis(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.analysis) {
      throw new Error('no analysis param');
    }

    try {
      const data = await this.aiService.getSkillsForAnalysis({
        analysis: body.analysis,
        skillsToSkip: body.skillsToSkip,
        customSkills: body.customSkills,
        query: body.query || body.analysis.title,
        companyDescription: body.companyDescription,
      });

      if (data.error) {
        console.log('error ', data.errorMessage);
        return { data: null, error: true, message: data.errorMessage };
      }
      if (!data.skills) {
        return { data: null, error: true };
      }
      return { skills: data.skills, error: false };
    } catch (error) {
      console.log('getSkillsForAnalysis error: ', error);
      return { data: null, error: true };
    }
  }

  @Post('ai/skill-validation')
  async aiSkillValidation(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.skill || !body.skill.type || !body.skill.name) {
      throw new Error('no skill param');
    }

    try {
      const result = await this.aiService.validateSkill(body.skill);
      return result;
    } catch (error) {
      console.log('aiSkillValidation error: ', error);
      return {
        isValid: false,
      };
    }
  }

  @Post('ai/certificate-description')
  async certificateDescription(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (
      !body.certificate ||
      !body.certificate.vendor ||
      !body.certificate.name
    ) {
      throw new Error('no certificate param with vendor and name');
    }

    try {
      const result = await this.aiService.generateCertificateDescription({
        vendor: body.certificate.vendor,
        name: body.certificate.name,
        forceWeb: body.forceWeb,
      });
      return result;
    } catch (error) {
      console.log('certificateDescription error: ', error);
      return { description: '', error: true };
    }
  }

  @Post('ai/generate-goal-plan')
  async generateGoalPlan(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (
      !body.user ||
      !body.user.jobTitle ||
      !body.user.skills ||
      !body.goal ||
      !body.goal.competenceName ||
      !body.goal.competenceType ||
      !body.goal.reachingType
    ) {
      throw new Error(
        'Missing required parameters: user (jobTitle, skills), goal (competenceName, competenceType, reachingType)',
      );
    }

    try {
      const result = await this.aiService.generateGoalPlan({
        companyDescription: body.companyDescription || '',
        user: body.user,
        goal: body.goal,
        existingGoals: body.existingGoals || [],
      });
      return {
        description: result.description,
        expectedGoalDurationInDays: result.expectedGoalDurationInDays,
        error: false,
      };
    } catch (error) {
      console.log('generateGoalPlan error: ', error);
      return { description: '', expectedGoalDurationInDays: 0, error: true };
    }
  }

  @Post('ai/regenerate-cv')
  async regenerateCv(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.cv || !body.member || !body.query) {
      throw new Error('Missing required parameters: cv, member, query');
    }

    try {
      const result = await this.aiService.regenerateCv({
        cv: body.cv,
        member: body.member,
        customerName: body.customerName || null,
        query: body.query,
        messages: body.messages || [],
      });
      return result;
    } catch (error) {
      console.log('regenerateCv error: ', error);
      return {
        updatedSections: {},
        changesSummary:
          'An error occurred while processing your request. Please try again.',
        error: true,
      };
    }
  }

  @Post('ai/enrich-certificate-metadata')
  async enrichCertificateMetadata(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (
      !body.certificate ||
      !body.certificate.vendor ||
      !body.certificate.name
    ) {
      throw new Error('no certificate param with vendor and name');
    }

    try {
      const result = await this.aiService.enrichCertificateMetadata(
        body.certificate,
      );
      return result;
    } catch (error) {
      console.log('enrichCertificateMetadata error: ', error);
      return { metadata: null, error: true };
    }
  }

  // outdated
  @Post('ai/skill-description')
  async aiSkillDesc(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }

    if (!body.skill || !body.skill.type || !body.skill.name) {
      throw new Error('no skill param');
    }

    try {
      const description = await this.aiService.generateSkillDescriptions(
        body.skill,
      );
      if (!description) {
        return { error: true };
      }

      return { description, error: false };
    } catch (error) {
      console.log('generateSkillDescriptions error: ', error);
      return { description: null, error: true };
    }
  }

  @Post('ai/lang-detection')
  async langDetection(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.skill || !body.skill.name) {
      throw new Error('no skill param');
    }

    try {
      const code = await this.aiService.detectLang(body.skill);

      if (!code) {
        return { error: true };
      }
      return { code, error: false };
    } catch (error) {
      console.log('langDetection error: ', error);
      return { data: null, error: true };
    }
  }

  @Post('ai/country-detection')
  async countryDetection(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.location) {
      throw new Error('no location param');
    }

    try {
      const countryName = await this.aiService.detectCountry(body.location);

      if (!countryName) {
        return { error: true, message: 'Country not found' };
      }

      return { country: countryName, error: false };
    } catch (error) {
      console.log('countryDetection error: ', error);
      return { data: null, error: true, message: 'Failed to detect country' };
    }
  }

  @Post('ai/get-team-data-from-query')
  async getTeamFromQuery(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.query) {
      throw new Error('no query param');
    }

    try {
      const team = await this.aiService.getTeamFromQuery({
        query: body.query,
      });

      if (!team) {
        return { error: false, team: '' };
      }
      return { team, error: false };
    } catch (error) {
      console.log('getRoleFromQuery error: ', error);
      return { data: null, error: true };
    }
  }

  @Post('ai/get-skills-for-topic')
  async getSkillsForTopic(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.query) {
      throw new Error('no query param');
    }

    try {
      const skillNames = await this.aiService.getSkillsForTopic({
        query: body.query,
        amount: body.amount,
        topic: body.topic,
      });

      if (!skillNames) {
        return { error: false, skillNames: [] };
      }
      return { skillNames, error: false };
    } catch (error) {
      console.log('getSkillsForTopic error: ', error);
      return { data: null, error: true };
    }
  }

  @Post('ai/get-ideas-from-skills')
  async getIdeasFromSkills(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.skills) {
      throw new Error('no query param');
    }

    try {
      const text = await this.aiService.getIdeasFromSkills({
        skills: body.skills,
        jobTitle: body.jobTitle,
      });

      if (!text) {
        return { error: false, text: '' };
      }
      return { text, error: false };
    } catch (error) {
      console.log('getIdeasFromSkills error: ', error);
      return { data: null, error: true };
    }
  }

  @Post('ai/generate-skills-summary')
  async generateSkillsSummary(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.skills) {
      throw new Error('no skills param');
    }

    try {
      const summary = await this.aiService.generateSkillsSummary(body.skills);

      if (!summary) {
        return { error: false, summary: '' };
      }
      return { summary, error: false };
    } catch (error) {
      console.log('generateSkillsSummary error: ', error);
      return { summary: null, error: true };
    }
  }
}
